lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      vitepress:
        specifier: 1.0.0-alpha.28
        version: 1.0.0-alpha.28(@algolia/client-search@5.30.0)(search-insights@2.17.3)
      vue:
        specifier: 3.2.44
        version: 3.2.44

packages:

  '@algolia/autocomplete-core@1.17.9':
    resolution: {integrity: sha512-O7BxrpLDPJWWHv/DLA9DRFWs+iY1uOJZkqUwjS5HSZAGcl0hIVCQ97LTLewiZmZ402JYUrun+8NqFP+hCknlbQ==}

  '@algolia/autocomplete-plugin-algolia-insights@1.17.9':
    resolution: {integrity: sha512-u1fEHkCbWF92DBeB/KHeMacsjsoI0wFhjZtlCq2ddZbAehshbZST6Hs0Avkc0s+4UyBGbMDnSuXHLuvRWK5iDQ==}
    peerDependencies:
      search-insights: '>= 1 < 3'

  '@algolia/autocomplete-preset-algolia@1.17.9':
    resolution: {integrity: sha512-Na1OuceSJeg8j7ZWn5ssMu/Ax3amtOwk76u4h5J4eK2Nx2KB5qt0Z4cOapCsxot9VcEN11ADV5aUSlQF4RhGjQ==}
    peerDependencies:
      '@algolia/client-search': '>= 4.9.1 < 6'
      algoliasearch: '>= 4.9.1 < 6'

  '@algolia/autocomplete-shared@1.17.9':
    resolution: {integrity: sha512-iDf05JDQ7I0b7JEA/9IektxN/80a2MZ1ToohfmNS3rfeuQnIKI3IJlIafD0xu4StbtQTghx9T3Maa97ytkXenQ==}
    peerDependencies:
      '@algolia/client-search': '>= 4.9.1 < 6'
      algoliasearch: '>= 4.9.1 < 6'

  '@algolia/client-abtesting@5.30.0':
    resolution: {integrity: sha512-Q3OQXYlTNqVUN/V1qXX8VIzQbLjP3yrRBO9m6NRe1CBALmoGHh9JrYosEGvfior28+DjqqU3Q+nzCSuf/bX0Gw==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-analytics@5.30.0':
    resolution: {integrity: sha512-/b+SAfHjYjx/ZVeVReCKTTnFAiZWOyvYLrkYpeNMraMT6akYRR8eC1AvFcvR60GLG/jytxcJAp42G8nN5SdcLg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-common@5.30.0':
    resolution: {integrity: sha512-tbUgvkp2d20mHPbM0+NPbLg6SzkUh0lADUUjzNCF+HiPkjFRaIW3NGMlESKw5ia4Oz6ZvFzyREquUX6rdkdJcQ==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-insights@5.30.0':
    resolution: {integrity: sha512-caXuZqJK761m32KoEAEkjkE2WF/zYg1McuGesWXiLSgfxwZZIAf+DljpiSToBUXhoPesvjcLtINyYUzbkwE0iw==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-personalization@5.30.0':
    resolution: {integrity: sha512-7K6P7TRBHLX1zTmwKDrIeBSgUidmbj6u3UW/AfroLRDGf9oZFytPKU49wg28lz/yulPuHY0nZqiwbyAxq9V17w==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-query-suggestions@5.30.0':
    resolution: {integrity: sha512-WMjWuBjYxJheRt7Ec5BFr33k3cV0mq2WzmH9aBf5W4TT8kUp34x91VRsYVaWOBRlxIXI8o/WbhleqSngiuqjLA==}
    engines: {node: '>= 14.0.0'}

  '@algolia/client-search@5.30.0':
    resolution: {integrity: sha512-puc1/LREfSqzgmrOFMY5L/aWmhYOlJ0TTpa245C0ZNMKEkdOkcimFbXTXQ8lZhzh+rlyFgR7cQGNtXJ5H0XgZg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/ingestion@1.30.0':
    resolution: {integrity: sha512-NfqiIKVgGKTLr6T9F81oqB39pPiEtILTy0z8ujxPKg2rCvI/qQeDqDWFBmQPElCfUTU6kk67QAgMkQ7T6fE+gg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/monitoring@1.30.0':
    resolution: {integrity: sha512-/eeM3aqLKro5KBZw0W30iIA6afkGa+bcpvEM0NDa92m5t3vil4LOmJI9FkgzfmSkF4368z/SZMOTPShYcaVXjA==}
    engines: {node: '>= 14.0.0'}

  '@algolia/recommend@5.30.0':
    resolution: {integrity: sha512-iWeAUWqw+xT+2IyUyTqnHCK+cyCKYV5+B6PXKdagc9GJJn6IaPs8vovwoC0Za5vKCje/aXQ24a2Z1pKpc/tdHg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-browser-xhr@5.30.0':
    resolution: {integrity: sha512-alo3ly0tdNLjfMSPz9dmNwYUFHx7guaz5dTGlIzVGnOiwLgIoM6NgA+MJLMcH6e1S7OpmE2AxOy78svlhst2tQ==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-fetch@5.30.0':
    resolution: {integrity: sha512-WOnTYUIY2InllHBy6HHMpGIOo7Or4xhYUx/jkoSK/kPIa1BRoFEHqa8v4pbKHtoG7oLvM2UAsylSnjVpIhGZXg==}
    engines: {node: '>= 14.0.0'}

  '@algolia/requester-node-http@5.30.0':
    resolution: {integrity: sha512-uSTUh9fxeHde1c7KhvZKUrivk90sdiDftC+rSKNFKKEU9TiIKAGA7B2oKC+AoMCqMymot1vW9SGbeESQPTZd0w==}
    engines: {node: '>= 14.0.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.0':
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/types@7.28.0':
    resolution: {integrity: sha512-jYnje+JyZG5YThjHiF28oT4SIZLnYOcSBb6+SDaFIyzDVSkXQmQQYclJ2R+YxcdmK0AX6x1E5OQNtuh3jHDrUg==}
    engines: {node: '>=6.9.0'}

  '@docsearch/css@3.9.0':
    resolution: {integrity: sha512-cQbnVbq0rrBwNAKegIac/t6a8nWoUAn8frnkLFW6YARaRmAQr5/Eoe6Ln2fqkUCZ40KpdrKbpSAmgrkviOxuWA==}

  '@docsearch/js@3.9.0':
    resolution: {integrity: sha512-4bKHcye6EkLgRE8ze0vcdshmEqxeiJM77M0JXjef7lrYZfSlMunrDOCqyLjiZyo1+c0BhUqA2QpFartIjuHIjw==}

  '@docsearch/react@3.9.0':
    resolution: {integrity: sha512-mb5FOZYZIkRQ6s/NWnM98k879vu5pscWqTLubLFBO87igYYT4VzVazh4h5o/zCvTIZgEt3PvsCOMOswOUo9yHQ==}
    peerDependencies:
      '@types/react': '>= 16.8.0 < 20.0.0'
      react: '>= 16.8.0 < 20.0.0'
      react-dom: '>= 16.8.0 < 20.0.0'
      search-insights: '>= 1 < 3'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      search-insights:
        optional: true

  '@esbuild/android-arm@0.15.18':
    resolution: {integrity: sha512-5GT+kcs2WVGjVs7+boataCkO5Fg0y4kCjzkB5bAip7H4jfnOS3dA6KPiww9W1OEKTKeAcUVhdZGvgI65OXmUnw==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/linux-loong64@0.15.18':
    resolution: {integrity: sha512-L4jVKS82XVhw2nvzLg/19ClLWg0y27ulRwuP7lcyL6AbUWB5aPglXY3M21mauDQMDfRLs8cQmeT03r/+X3cZYQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@types/web-bluetooth@0.0.16':
    resolution: {integrity: sha512-oh8q2Zc32S6gd/j50GowEjKLoOVOwHP/bWVjKJInBwQqdOYMdPrf1oVlelTlyfFK3CKxL1uahMDAr+vy8T7yMQ==}

  '@vitejs/plugin-vue@3.2.0':
    resolution: {integrity: sha512-E0tnaL4fr+qkdCNxJ+Xd0yM31UwMkQje76fsDVBBUCoGOUPexu2VDUYHL8P4CwV+zMvWw6nlRw19OnRKmYAJpw==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^3.0.0
      vue: ^3.2.25

  '@vue/compiler-core@3.2.44':
    resolution: {integrity: sha512-TwzeVSnaklb8wIvMtwtkPkt9wnU+XD70xJ7N9+eIHtjKAG7OoZttm+14ZL6vWOL+2RcMtSZ+cYH+gvkUqsrmSQ==}

  '@vue/compiler-dom@3.2.44':
    resolution: {integrity: sha512-wPDR+gOn2Qi7SudPJ+gE62vuO/aKXIiIFALvHpztXmDdbAHGy3CDfmBgOGchTgTlSeDJHe9olEMkgOdmyXTjUg==}

  '@vue/compiler-sfc@3.2.44':
    resolution: {integrity: sha512-8cFZcUWlrtnfM/GlRwYJdlfgbEOy0OZ/osLDU3h/wJu24HuYAc7QIML1USaKqiZzkjOaTd4y8mvYvcWXq3o5dA==}

  '@vue/compiler-ssr@3.2.44':
    resolution: {integrity: sha512-tAkUFLgvxds3l5KPyAH77OIYrEeLngNYQfWA9GocHiy2nlyajjqAH/Jq93Bq29Y20GeJzblmRp9DVYCVkJ5Rsw==}

  '@vue/devtools-api@6.6.4':
    resolution: {integrity: sha512-sGhTPMuXqZ1rVOk32RylztWkfXTRhuS7vgAKv0zjqk8gbsHkJ7xfFf+jbySxt7tWObEJwyKaHMikV/WGDiQm8g==}

  '@vue/reactivity-transform@3.2.44':
    resolution: {integrity: sha512-WGbEiXaS2qAOTS9Z3kKk2Nk4bi8OUl73Sih+h0XV9RTUATnaJSEQedveHUDQnHyXiZwyBMKosrxJg8aThHO/rw==}

  '@vue/reactivity@3.2.44':
    resolution: {integrity: sha512-Fe0s52fTsPl+RSdvoqUZ3HRKlaVsKhIh1mea5EWOedFvZCjnymzlj3YC1wZMxi89qXRFSdEASVA/BWUGypk0Ig==}

  '@vue/runtime-core@3.2.44':
    resolution: {integrity: sha512-uwEV1cttL33k2dC+CNGYhKEYqGejT9KmgQ+4n/LmYUfZ1Gorl8F32DlIX+1pANyGHL1tBAisqHDxKyQBp2oBNA==}

  '@vue/runtime-dom@3.2.44':
    resolution: {integrity: sha512-LDzNwXpU/nSpxrLk5jS0bfStgt88msgsgFzj6vHrl7es3QktIrCGybQS5CB/p/TO0q98iAiYtEVmi+Lej7Vgjg==}

  '@vue/server-renderer@3.2.44':
    resolution: {integrity: sha512-3+ArN07UgOAdbGKIp3uVqeC3bnR3J324QNjPR6vxHbLrTlkibFv8QNled/ux3fVq0KDCkVVKGOKB2V4sCIYOgg==}
    peerDependencies:
      vue: 3.2.44

  '@vue/shared@3.2.44':
    resolution: {integrity: sha512-mGZ44bnn0zpZ36nXtxbrBPno43yr96wjQE1dBEKS1Sieugt27HS4OGZVBRIgsdGzosB7vqZAvu0ttu1FDVdolA==}

  '@vueuse/core@9.13.0':
    resolution: {integrity: sha512-pujnclbeHWxxPRqXWmdkKV5OX4Wk4YeK7wusHqRwU0Q7EFusHoqNA/aPhB6KCh9hEqJkLAJo7bb0Lh9b+OIVzw==}

  '@vueuse/metadata@9.13.0':
    resolution: {integrity: sha512-gdU7TKNAUVlXXLbaF+ZCfte8BjRJQWPCa2J55+7/h+yDtzw3vOoGQDRXzI6pyKyo6bXFT5/QoPE4hAknExjRLQ==}

  '@vueuse/shared@9.13.0':
    resolution: {integrity: sha512-UrnhU+Cnufu4S6JLCPZnkWh0WwZGUp72ktOF2DFptMlOs3TOdVv8xJN53zhHGARmVOsz5KqOls09+J1NR6sBKw==}

  algoliasearch@5.30.0:
    resolution: {integrity: sha512-ILSdPX4je0n5WUKD34TMe57/eqiXUzCIjAsdtLQYhomqOjTtFUg1s6dE7kUegc4Mc43Xr7IXYlMutU9HPiYfdw==}
    engines: {node: '>= 14.0.0'}

  body-scroll-lock@4.0.0-beta.0:
    resolution: {integrity: sha512-a7tP5+0Mw3YlUJcGAKUqIBkYYGlYxk2fnCasq/FUph1hadxlTRjF+gAcZksxANnaMnALjxEddmSi/H3OR8ugcQ==}

  csstype@2.6.21:
    resolution: {integrity: sha512-Z1PhmomIfypOpoMjRQB70jfvy/wxT50qW08YXO5lMIJkrdq4yOTR+AW7FqutScmB9NkLwxo+jU+kZLbofZZq/w==}

  esbuild-android-64@0.15.18:
    resolution: {integrity: sha512-wnpt3OXRhcjfIDSZu9bnzT4/TNTDsOUvip0foZOUBG7QbSt//w3QV4FInVJxNhKc/ErhUxc5z4QjHtMi7/TbgA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  esbuild-android-arm64@0.15.18:
    resolution: {integrity: sha512-G4xu89B8FCzav9XU8EjsXacCKSG2FT7wW9J6hOc18soEHJdtWu03L3TQDGf0geNxfLTtxENKBzMSq9LlbjS8OQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  esbuild-darwin-64@0.15.18:
    resolution: {integrity: sha512-2WAvs95uPnVJPuYKP0Eqx+Dl/jaYseZEUUT1sjg97TJa4oBtbAKnPnl3b5M9l51/nbx7+QAEtuummJZW0sBEmg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  esbuild-darwin-arm64@0.15.18:
    resolution: {integrity: sha512-tKPSxcTJ5OmNb1btVikATJ8NftlyNlc8BVNtyT/UAr62JFOhwHlnoPrhYWz09akBLHI9nElFVfWSTSRsrZiDUA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  esbuild-freebsd-64@0.15.18:
    resolution: {integrity: sha512-TT3uBUxkteAjR1QbsmvSsjpKjOX6UkCstr8nMr+q7zi3NuZ1oIpa8U41Y8I8dJH2fJgdC3Dj3CXO5biLQpfdZA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  esbuild-freebsd-arm64@0.15.18:
    resolution: {integrity: sha512-R/oVr+X3Tkh+S0+tL41wRMbdWtpWB8hEAMsOXDumSSa6qJR89U0S/PpLXrGF7Wk/JykfpWNokERUpCeHDl47wA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  esbuild-linux-32@0.15.18:
    resolution: {integrity: sha512-lphF3HiCSYtaa9p1DtXndiQEeQDKPl9eN/XNoBf2amEghugNuqXNZA/ZovthNE2aa4EN43WroO0B85xVSjYkbg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  esbuild-linux-64@0.15.18:
    resolution: {integrity: sha512-hNSeP97IviD7oxLKFuii5sDPJ+QHeiFTFLoLm7NZQligur8poNOWGIgpQ7Qf8Balb69hptMZzyOBIPtY09GZYw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  esbuild-linux-arm64@0.15.18:
    resolution: {integrity: sha512-54qr8kg/6ilcxd+0V3h9rjT4qmjc0CccMVWrjOEM/pEcUzt8X62HfBSeZfT2ECpM7104mk4yfQXkosY8Quptug==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  esbuild-linux-arm@0.15.18:
    resolution: {integrity: sha512-UH779gstRblS4aoS2qpMl3wjg7U0j+ygu3GjIeTonCcN79ZvpPee12Qun3vcdxX+37O5LFxz39XeW2I9bybMVA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  esbuild-linux-mips64le@0.15.18:
    resolution: {integrity: sha512-Mk6Ppwzzz3YbMl/ZZL2P0q1tnYqh/trYZ1VfNP47C31yT0K8t9s7Z077QrDA/guU60tGNp2GOwCQnp+DYv7bxQ==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  esbuild-linux-ppc64le@0.15.18:
    resolution: {integrity: sha512-b0XkN4pL9WUulPTa/VKHx2wLCgvIAbgwABGnKMY19WhKZPT+8BxhZdqz6EgkqCLld7X5qiCY2F/bfpUUlnFZ9w==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  esbuild-linux-riscv64@0.15.18:
    resolution: {integrity: sha512-ba2COaoF5wL6VLZWn04k+ACZjZ6NYniMSQStodFKH/Pu6RxzQqzsmjR1t9QC89VYJxBeyVPTaHuBMCejl3O/xg==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  esbuild-linux-s390x@0.15.18:
    resolution: {integrity: sha512-VbpGuXEl5FCs1wDVp93O8UIzl3ZrglgnSQ+Hu79g7hZu6te6/YHgVJxCM2SqfIila0J3k0csfnf8VD2W7u2kzQ==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  esbuild-netbsd-64@0.15.18:
    resolution: {integrity: sha512-98ukeCdvdX7wr1vUYQzKo4kQ0N2p27H7I11maINv73fVEXt2kyh4K4m9f35U1K43Xc2QGXlzAw0K9yoU7JUjOg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  esbuild-openbsd-64@0.15.18:
    resolution: {integrity: sha512-yK5NCcH31Uae076AyQAXeJzt/vxIo9+omZRKj1pauhk3ITuADzuOx5N2fdHrAKPxN+zH3w96uFKlY7yIn490xQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  esbuild-sunos-64@0.15.18:
    resolution: {integrity: sha512-On22LLFlBeLNj/YF3FT+cXcyKPEI263nflYlAhz5crxtp3yRG1Ugfr7ITyxmCmjm4vbN/dGrb/B7w7U8yJR9yw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  esbuild-windows-32@0.15.18:
    resolution: {integrity: sha512-o+eyLu2MjVny/nt+E0uPnBxYuJHBvho8vWsC2lV61A7wwTWC3jkN2w36jtA+yv1UgYkHRihPuQsL23hsCYGcOQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  esbuild-windows-64@0.15.18:
    resolution: {integrity: sha512-qinug1iTTaIIrCorAUjR0fcBk24fjzEedFYhhispP8Oc7SFvs+XeW3YpAKiKp8dRpizl4YYAhxMjlftAMJiaUw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  esbuild-windows-arm64@0.15.18:
    resolution: {integrity: sha512-q9bsYzegpZcLziq0zgUi5KqGVtfhjxGbnksaBFYmWLxeV/S1fK4OLdq2DFYnXcLMjlZw2L0jLsk1eGoB522WXQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  esbuild@0.15.18:
    resolution: {integrity: sha512-x/R72SmW3sSFRm5zrrIjAhCeQSAWoni3CmHEqfQrZIQTM3lVCdehdwuIqaOtfC2slvpdlLa62GYoN8SxT23m6Q==}
    engines: {node: '>=12'}
    hasBin: true

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  jsonc-parser@3.3.1:
    resolution: {integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==}

  magic-string@0.25.9:
    resolution: {integrity: sha512-RmF0AsMzgt25qzqqLc1+MbHmhdx0ojF2Fvs4XnOqz2ZOBXzzkEwc/dJQZCYHAn7v1jbVOjAZfK8msRn4BxO4VQ==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  preact@10.26.9:
    resolution: {integrity: sha512-SSjF9vcnF27mJK1XyFMNJzFd5u3pQiATFqoaDy03XuN00u4ziveVVEGt5RKJrDR8MHE/wJo9Nnad56RLzS2RMA==}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  rollup@2.79.2:
    resolution: {integrity: sha512-fS6iqSPZDs3dr/y7Od6y5nha8dW1YnbgtsyotCVvoFGKbERG++CVRFv1meyGDE1SNItQA8BrnCw7ScdAhRJ3XQ==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  search-insights@2.17.3:
    resolution: {integrity: sha512-RQPdCYTa8A68uM2jwxoY842xDhvx3E5LFL1LxvxCNMev4o5mLuokczhzjAgGwUZBAmOKZknArSxLKmXtIi2AxQ==}

  shiki@0.11.1:
    resolution: {integrity: sha512-EugY9VASFuDqOexOgXR18ZV+TbFrQHeCpEYaXamO+SZlsnT/2LxuLBX25GGtIrwaEVFXUAbUQ601SWE2rMwWHA==}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==}
    deprecated: Please use @jridgewell/sourcemap-codec instead

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  vite@3.2.11:
    resolution: {integrity: sha512-K/jGKL/PgbIgKCiJo5QbASQhFiV02X9Jh+Qq0AKCRCRKZtOTVi4t6wh75FDpGf2N9rYOnzH87OEFQNaFy6pdxQ==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vitepress@1.0.0-alpha.28:
    resolution: {integrity: sha512-pvbLssDMgLUN1terajmPlFBxHSDGO4DqwexKbjFyr7LeELerVuwGrG6F2J1hxmwOlbpLd1kHXEDqGm9JX/kTDQ==}
    hasBin: true

  vscode-oniguruma@1.7.0:
    resolution: {integrity: sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==}

  vscode-textmate@6.0.0:
    resolution: {integrity: sha512-gu73tuZfJgu+mvCSy4UZwd2JXykjK9zAZsfmDeut5dx/1a7FeTk0XwJsSuqQn+cuMCGVbIBfl+s53X4T19DnzQ==}

  vue-demi@0.14.10:
    resolution: {integrity: sha512-nMZBOwuzabUO0nLgIcc6rycZEebF6eeUfaiQx9+WSk8e29IbLvPU9feI6tqW4kTo3hvoYAJkMh8n8D0fuISphg==}
    engines: {node: '>=12'}
    hasBin: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true

  vue@3.2.44:
    resolution: {integrity: sha512-nyNtFDh+0TpRgYCUVfPD1mJ9PpIsCPXaOF4DeGNIT5vQ4X23ykflGq3Sy2P+tEt1/pQZxZnAysuRKwyhNj+Cjw==}

snapshots:

  '@algolia/autocomplete-core@1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-plugin-algolia-insights': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)(search-insights@2.17.3)
      '@algolia/autocomplete-shared': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)
    transitivePeerDependencies:
      - '@algolia/client-search'
      - algoliasearch
      - search-insights

  '@algolia/autocomplete-plugin-algolia-insights@1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-shared': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)
      search-insights: 2.17.3
    transitivePeerDependencies:
      - '@algolia/client-search'
      - algoliasearch

  '@algolia/autocomplete-preset-algolia@1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)':
    dependencies:
      '@algolia/autocomplete-shared': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)
      '@algolia/client-search': 5.30.0
      algoliasearch: 5.30.0

  '@algolia/autocomplete-shared@1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)':
    dependencies:
      '@algolia/client-search': 5.30.0
      algoliasearch: 5.30.0

  '@algolia/client-abtesting@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/client-analytics@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/client-common@5.30.0': {}

  '@algolia/client-insights@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/client-personalization@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/client-query-suggestions@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/client-search@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/ingestion@1.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/monitoring@1.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/recommend@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  '@algolia/requester-browser-xhr@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0

  '@algolia/requester-fetch@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0

  '@algolia/requester-node-http@5.30.0':
    dependencies:
      '@algolia/client-common': 5.30.0

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/parser@7.28.0':
    dependencies:
      '@babel/types': 7.28.0

  '@babel/types@7.28.0':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@docsearch/css@3.9.0': {}

  '@docsearch/js@3.9.0(@algolia/client-search@5.30.0)(search-insights@2.17.3)':
    dependencies:
      '@docsearch/react': 3.9.0(@algolia/client-search@5.30.0)(search-insights@2.17.3)
      preact: 10.26.9
    transitivePeerDependencies:
      - '@algolia/client-search'
      - '@types/react'
      - react
      - react-dom
      - search-insights

  '@docsearch/react@3.9.0(@algolia/client-search@5.30.0)(search-insights@2.17.3)':
    dependencies:
      '@algolia/autocomplete-core': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)(search-insights@2.17.3)
      '@algolia/autocomplete-preset-algolia': 1.17.9(@algolia/client-search@5.30.0)(algoliasearch@5.30.0)
      '@docsearch/css': 3.9.0
      algoliasearch: 5.30.0
    optionalDependencies:
      search-insights: 2.17.3
    transitivePeerDependencies:
      - '@algolia/client-search'

  '@esbuild/android-arm@0.15.18':
    optional: true

  '@esbuild/linux-loong64@0.15.18':
    optional: true

  '@types/web-bluetooth@0.0.16': {}

  '@vitejs/plugin-vue@3.2.0(vite@3.2.11)(vue@3.2.44)':
    dependencies:
      vite: 3.2.11
      vue: 3.2.44

  '@vue/compiler-core@3.2.44':
    dependencies:
      '@babel/parser': 7.28.0
      '@vue/shared': 3.2.44
      estree-walker: 2.0.2
      source-map: 0.6.1

  '@vue/compiler-dom@3.2.44':
    dependencies:
      '@vue/compiler-core': 3.2.44
      '@vue/shared': 3.2.44

  '@vue/compiler-sfc@3.2.44':
    dependencies:
      '@babel/parser': 7.28.0
      '@vue/compiler-core': 3.2.44
      '@vue/compiler-dom': 3.2.44
      '@vue/compiler-ssr': 3.2.44
      '@vue/reactivity-transform': 3.2.44
      '@vue/shared': 3.2.44
      estree-walker: 2.0.2
      magic-string: 0.25.9
      postcss: 8.5.6
      source-map: 0.6.1

  '@vue/compiler-ssr@3.2.44':
    dependencies:
      '@vue/compiler-dom': 3.2.44
      '@vue/shared': 3.2.44

  '@vue/devtools-api@6.6.4': {}

  '@vue/reactivity-transform@3.2.44':
    dependencies:
      '@babel/parser': 7.28.0
      '@vue/compiler-core': 3.2.44
      '@vue/shared': 3.2.44
      estree-walker: 2.0.2
      magic-string: 0.25.9

  '@vue/reactivity@3.2.44':
    dependencies:
      '@vue/shared': 3.2.44

  '@vue/runtime-core@3.2.44':
    dependencies:
      '@vue/reactivity': 3.2.44
      '@vue/shared': 3.2.44

  '@vue/runtime-dom@3.2.44':
    dependencies:
      '@vue/runtime-core': 3.2.44
      '@vue/shared': 3.2.44
      csstype: 2.6.21

  '@vue/server-renderer@3.2.44(vue@3.2.44)':
    dependencies:
      '@vue/compiler-ssr': 3.2.44
      '@vue/shared': 3.2.44
      vue: 3.2.44

  '@vue/shared@3.2.44': {}

  '@vueuse/core@9.13.0(vue@3.2.44)':
    dependencies:
      '@types/web-bluetooth': 0.0.16
      '@vueuse/metadata': 9.13.0
      '@vueuse/shared': 9.13.0(vue@3.2.44)
      vue-demi: 0.14.10(vue@3.2.44)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  '@vueuse/metadata@9.13.0': {}

  '@vueuse/shared@9.13.0(vue@3.2.44)':
    dependencies:
      vue-demi: 0.14.10(vue@3.2.44)
    transitivePeerDependencies:
      - '@vue/composition-api'
      - vue

  algoliasearch@5.30.0:
    dependencies:
      '@algolia/client-abtesting': 5.30.0
      '@algolia/client-analytics': 5.30.0
      '@algolia/client-common': 5.30.0
      '@algolia/client-insights': 5.30.0
      '@algolia/client-personalization': 5.30.0
      '@algolia/client-query-suggestions': 5.30.0
      '@algolia/client-search': 5.30.0
      '@algolia/ingestion': 1.30.0
      '@algolia/monitoring': 1.30.0
      '@algolia/recommend': 5.30.0
      '@algolia/requester-browser-xhr': 5.30.0
      '@algolia/requester-fetch': 5.30.0
      '@algolia/requester-node-http': 5.30.0

  body-scroll-lock@4.0.0-beta.0: {}

  csstype@2.6.21: {}

  esbuild-android-64@0.15.18:
    optional: true

  esbuild-android-arm64@0.15.18:
    optional: true

  esbuild-darwin-64@0.15.18:
    optional: true

  esbuild-darwin-arm64@0.15.18:
    optional: true

  esbuild-freebsd-64@0.15.18:
    optional: true

  esbuild-freebsd-arm64@0.15.18:
    optional: true

  esbuild-linux-32@0.15.18:
    optional: true

  esbuild-linux-64@0.15.18:
    optional: true

  esbuild-linux-arm64@0.15.18:
    optional: true

  esbuild-linux-arm@0.15.18:
    optional: true

  esbuild-linux-mips64le@0.15.18:
    optional: true

  esbuild-linux-ppc64le@0.15.18:
    optional: true

  esbuild-linux-riscv64@0.15.18:
    optional: true

  esbuild-linux-s390x@0.15.18:
    optional: true

  esbuild-netbsd-64@0.15.18:
    optional: true

  esbuild-openbsd-64@0.15.18:
    optional: true

  esbuild-sunos-64@0.15.18:
    optional: true

  esbuild-windows-32@0.15.18:
    optional: true

  esbuild-windows-64@0.15.18:
    optional: true

  esbuild-windows-arm64@0.15.18:
    optional: true

  esbuild@0.15.18:
    optionalDependencies:
      '@esbuild/android-arm': 0.15.18
      '@esbuild/linux-loong64': 0.15.18
      esbuild-android-64: 0.15.18
      esbuild-android-arm64: 0.15.18
      esbuild-darwin-64: 0.15.18
      esbuild-darwin-arm64: 0.15.18
      esbuild-freebsd-64: 0.15.18
      esbuild-freebsd-arm64: 0.15.18
      esbuild-linux-32: 0.15.18
      esbuild-linux-64: 0.15.18
      esbuild-linux-arm: 0.15.18
      esbuild-linux-arm64: 0.15.18
      esbuild-linux-mips64le: 0.15.18
      esbuild-linux-ppc64le: 0.15.18
      esbuild-linux-riscv64: 0.15.18
      esbuild-linux-s390x: 0.15.18
      esbuild-netbsd-64: 0.15.18
      esbuild-openbsd-64: 0.15.18
      esbuild-sunos-64: 0.15.18
      esbuild-windows-32: 0.15.18
      esbuild-windows-64: 0.15.18
      esbuild-windows-arm64: 0.15.18

  estree-walker@2.0.2: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  jsonc-parser@3.3.1: {}

  magic-string@0.25.9:
    dependencies:
      sourcemap-codec: 1.4.8

  nanoid@3.3.11: {}

  path-parse@1.0.7: {}

  picocolors@1.1.1: {}

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  preact@10.26.9: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  rollup@2.79.2:
    optionalDependencies:
      fsevents: 2.3.3

  search-insights@2.17.3: {}

  shiki@0.11.1:
    dependencies:
      jsonc-parser: 3.3.1
      vscode-oniguruma: 1.7.0
      vscode-textmate: 6.0.0

  source-map-js@1.2.1: {}

  source-map@0.6.1: {}

  sourcemap-codec@1.4.8: {}

  supports-preserve-symlinks-flag@1.0.0: {}

  vite@3.2.11:
    dependencies:
      esbuild: 0.15.18
      postcss: 8.5.6
      resolve: 1.22.10
      rollup: 2.79.2
    optionalDependencies:
      fsevents: 2.3.3

  vitepress@1.0.0-alpha.28(@algolia/client-search@5.30.0)(search-insights@2.17.3):
    dependencies:
      '@docsearch/css': 3.9.0
      '@docsearch/js': 3.9.0(@algolia/client-search@5.30.0)(search-insights@2.17.3)
      '@vitejs/plugin-vue': 3.2.0(vite@3.2.11)(vue@3.2.44)
      '@vue/devtools-api': 6.6.4
      '@vueuse/core': 9.13.0(vue@3.2.44)
      body-scroll-lock: 4.0.0-beta.0
      shiki: 0.11.1
      vite: 3.2.11
      vue: 3.2.44
    transitivePeerDependencies:
      - '@algolia/client-search'
      - '@types/node'
      - '@types/react'
      - '@vue/composition-api'
      - less
      - react
      - react-dom
      - sass
      - search-insights
      - stylus
      - sugarss
      - terser

  vscode-oniguruma@1.7.0: {}

  vscode-textmate@6.0.0: {}

  vue-demi@0.14.10(vue@3.2.44):
    dependencies:
      vue: 3.2.44

  vue@3.2.44:
    dependencies:
      '@vue/compiler-dom': 3.2.44
      '@vue/compiler-sfc': 3.2.44
      '@vue/runtime-dom': 3.2.44
      '@vue/server-renderer': 3.2.44(vue@3.2.44)
      '@vue/shared': 3.2.44
