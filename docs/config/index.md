# 基础配置

VitePress 的配置文件位于 `docs/.vitepress/config.ts`，使用 TypeScript 编写。

## 站点配置

### 基本信息

```typescript
export default defineConfig({
  title: '站点标题',
  description: '站点描述',
  lang: 'zh-CN',
  base: '/',
  
  // 清理 URL
  cleanUrls: true,
  
  // 最后更新时间
  lastUpdated: true
})
```

### 头部配置

```typescript
export default defineConfig({
  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
    ['meta', { name: 'theme-color', content: '#3c8772' }],
    ['meta', { property: 'og:type', content: 'website' }],
    ['meta', { property: 'og:locale', content: 'zh-CN' }]
  ]
})
```

### Markdown 配置

```typescript
export default defineConfig({
  markdown: {
    // 行号显示
    lineNumbers: true,
    
    // 代码块主题
    theme: 'github-dark',
    
    // 自定义锚点
    anchor: {
      permalink: true,
      permalinkBefore: true,
      permalinkSymbol: '#'
    }
  }
})
```

## 主题配置

### 导航栏

```typescript
export default defineConfig({
  themeConfig: {
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/' },
      {
        text: '下拉菜单',
        items: [
          { text: '选项1', link: '/item-1' },
          { text: '选项2', link: '/item-2' }
        ]
      }
    ]
  }
})
```

### 侧边栏

```typescript
export default defineConfig({
  themeConfig: {
    sidebar: {
      '/guide/': [
        {
          text: '指南',
          items: [
            { text: '介绍', link: '/guide/' },
            { text: '快速开始', link: '/guide/getting-started' }
          ]
        }
      ],
      '/config/': [
        {
          text: '配置',
          items: [
            { text: '基础配置', link: '/config/' },
            { text: '主题配置', link: '/config/theme' }
          ]
        }
      ]
    }
  }
})
```

### 社交链接

```typescript
export default defineConfig({
  themeConfig: {
    socialLinks: [
      { icon: 'github', link: 'https://github.com/vuejs/vitepress' },
      { icon: 'twitter', link: 'https://twitter.com/vuejs' },
      { icon: 'discord', link: 'https://discord.gg/vue' }
    ]
  }
})
```

### 页脚

```typescript
export default defineConfig({
  themeConfig: {
    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2023-present Your Name'
    }
  }
})
```

## 搜索配置

### 本地搜索

```typescript
export default defineConfig({
  themeConfig: {
    search: {
      provider: 'local'
    }
  }
})
```

### Algolia 搜索

```typescript
export default defineConfig({
  themeConfig: {
    search: {
      provider: 'algolia',
      options: {
        appId: 'YOUR_APP_ID',
        apiKey: 'YOUR_API_KEY',
        indexName: 'YOUR_INDEX_NAME'
      }
    }
  }
})
```

## 部署配置

### GitHub Pages

```typescript
export default defineConfig({
  base: '/your-repo-name/',
  
  // 构建输出目录
  outDir: '../dist'
})
```

### Vercel

```typescript
export default defineConfig({
  // 默认配置即可
})
```

## 高级配置

### 自定义容器

```typescript
export default defineConfig({
  markdown: {
    container: {
      tipLabel: '提示',
      warningLabel: '警告',
      dangerLabel: '危险',
      infoLabel: '信息',
      detailsLabel: '详细信息'
    }
  }
})
```

### PWA 支持

```typescript
import { defineConfig } from 'vitepress'
import { withPwa } from '@vite-pwa/vitepress'

export default withPwa(
  defineConfig({
    // 你的配置
  }),
  {
    // PWA 配置
    registerType: 'autoUpdate',
    workbox: {
      globPatterns: ['**/*.{css,js,html,svg,png,ico,txt,woff2}']
    }
  }
)
```
