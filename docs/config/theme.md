# 主题配置

VitePress 提供了丰富的主题配置选项，让你可以轻松定制站点的外观和行为。

## 布局配置

### 首页布局

```yaml
---
layout: home

hero:
  name: "站点名称"
  text: "站点标语"
  tagline: "详细描述"
  image:
    src: /logo.png
    alt: Logo
  actions:
    - theme: brand
      text: 开始使用
      link: /guide/getting-started
    - theme: alt
      text: 查看 GitHub
      link: https://github.com/vuejs/vitepress

features:
  - title: 特性 1
    details: 特性描述
  - title: 特性 2
    details: 特性描述
---
```

### 文档布局

```yaml
---
layout: doc
title: 页面标题
description: 页面描述
---
```

### 页面布局

```yaml
---
layout: page
---
```

## 外观定制

### 自定义 CSS

创建 `docs/.vitepress/theme/style.css`：

```css
:root {
  /* 品牌色 */
  --vp-c-brand-1: #646cff;
  --vp-c-brand-2: #747bff;
  --vp-c-brand-3: #9499ff;
  
  /* 背景色 */
  --vp-c-bg: #ffffff;
  --vp-c-bg-alt: #f6f6f7;
  --vp-c-bg-elv: #ffffff;
  
  /* 文字色 */
  --vp-c-text-1: #213547;
  --vp-c-text-2: #476582;
  --vp-c-text-3: #7c8b9a;
}

.dark {
  --vp-c-bg: #1b1b1f;
  --vp-c-bg-alt: #161618;
  --vp-c-bg-elv: #202127;
  
  --vp-c-text-1: rgba(255, 255, 245, 0.86);
  --vp-c-text-2: rgba(235, 235, 245, 0.6);
  --vp-c-text-3: rgba(235, 235, 245, 0.38);
}
```

### 自定义主题

创建 `docs/.vitepress/theme/index.ts`：

```typescript
import DefaultTheme from 'vitepress/theme'
import './style.css'

export default {
  extends: DefaultTheme,
  enhanceApp({ app, router, siteData }) {
    // 注册全局组件
    // app.component('MyComponent', MyComponent)
  }
}
```

## 组件定制

### 自定义组件

```vue
<!-- docs/.vitepress/theme/components/MyComponent.vue -->
<template>
  <div class="my-component">
    <h3>{{ title }}</h3>
    <p>{{ description }}</p>
  </div>
</template>

<script setup>
defineProps({
  title: String,
  description: String
})
</script>

<style scoped>
.my-component {
  padding: 1rem;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
}
</style>
```

在 Markdown 中使用：

```markdown
<MyComponent title="标题" description="描述" />
```

### 插槽定制

```typescript
// docs/.vitepress/theme/index.ts
import DefaultTheme from 'vitepress/theme'
import MyLayout from './MyLayout.vue'

export default {
  extends: DefaultTheme,
  Layout: MyLayout
}
```

```vue
<!-- docs/.vitepress/theme/MyLayout.vue -->
<template>
  <Layout>
    <template #nav-bar-title-before>
      <img src="/logo.png" alt="Logo" class="logo" />
    </template>
    
    <template #sidebar-nav-before>
      <div class="custom-sidebar-top">
        自定义侧边栏顶部内容
      </div>
    </template>
    
    <template #doc-footer-before>
      <div class="custom-footer">
        自定义页脚内容
      </div>
    </template>
  </Layout>
</template>

<script setup>
import DefaultTheme from 'vitepress/theme'
const { Layout } = DefaultTheme
</script>
```

## 响应式配置

### 断点设置

```css
/* 移动端 */
@media (max-width: 768px) {
  .VPHero .name {
    font-size: 2rem;
  }
}

/* 平板 */
@media (min-width: 768px) and (max-width: 1024px) {
  .VPSidebar {
    width: 280px;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .VPContent {
    max-width: 1200px;
  }
}
```

## 国际化

### 多语言配置

```typescript
export default defineConfig({
  locales: {
    root: {
      label: '简体中文',
      lang: 'zh-CN'
    },
    en: {
      label: 'English',
      lang: 'en-US',
      link: '/en/',
      
      themeConfig: {
        nav: [
          { text: 'Home', link: '/en/' },
          { text: 'Guide', link: '/en/guide/' }
        ]
      }
    }
  }
})
```

### 语言切换

```typescript
export default defineConfig({
  themeConfig: {
    langMenuLabel: '选择语言',
    returnToTopLabel: '返回顶部',
    sidebarMenuLabel: '菜单',
    darkModeSwitchLabel: '主题',
    lightModeSwitchTitle: '切换到浅色模式',
    darkModeSwitchTitle: '切换到深色模式'
  }
})
```

## 插件集成

### 代码复制

```typescript
export default defineConfig({
  markdown: {
    codeTransformers: [
      {
        postprocess(code) {
          return code.replace(/\[!!code/g, '[!code')
        }
      }
    ]
  }
})
```

### 数学公式

```bash
pnpm add markdown-it-mathjax3
```

```typescript
import mathjax3 from 'markdown-it-mathjax3'

export default defineConfig({
  markdown: {
    config: (md) => {
      md.use(mathjax3)
    }
  }
})
```

### 图表支持

```bash
pnpm add mermaid
```

```typescript
export default defineConfig({
  markdown: {
    config: (md) => {
      md.use(require('markdown-it-mermaid'))
    }
  }
})
```
