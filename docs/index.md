---
layout: home

hero:
  name: "VitePress Hello"
  text: "快速、简洁的静态站点生成器"
  tagline: 基于 Vite 和 Vue 构建的现代文档站点
  actions:
    - theme: brand
      text: 快速开始
      link: /guide/getting-started
    - theme: alt
      text: 查看指南
      link: /guide/

features:
  - title: 🚀 极速开发
    details: 基于 Vite 的快速热重载，让开发体验更流畅
  - title: 📝 Markdown 增强
    details: 支持 Vue 组件、代码高亮、数学公式等丰富功能
  - title: 🎨 主题定制
    details: 灵活的主题系统，轻松定制你的站点外观
  - title: 📱 响应式设计
    details: 完美适配桌面端和移动端设备
---
