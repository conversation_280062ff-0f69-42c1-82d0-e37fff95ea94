import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'VitePress Hello',
  description: 'A VitePress example project',

  themeConfig: {
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/' },
      { text: '配置', link: '/config/' }
    ],

    sidebar: [
      {
        text: '开始',
        items: [
          { text: '介绍', link: '/guide/' },
          { text: '快速开始', link: '/guide/getting-started' },
          { text: '交互式示例', link: '/guide/interactive-examples' },
          { text: '高级示例', link: '/guide/advanced-examples' },
          { text: '组件集成', link: '/guide/component-integration' }
        ]
      },
      {
        text: '配置',
        items: [
          { text: '基础配置', link: '/config/' },
          { text: '主题配置', link: '/config/theme' }
        ]
      }
    ],

    socialLinks: [
      { icon: 'github', link: 'https://github.com/vuejs/vitepress' }
    ],

    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2023-present VitePress'
    }
  }
})
