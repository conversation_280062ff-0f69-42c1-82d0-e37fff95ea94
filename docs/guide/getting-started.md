# 快速开始

本指南将帮助你快速搭建一个 VitePress 站点。

## 安装

### 前提条件

- [Node.js](https://nodejs.org/) 版本 16 或更高
- 推荐使用 [pnpm](https://pnpm.io/) 作为包管理器

### 创建项目

```bash
# 使用 pnpm 创建项目
pnpm create vitepress

# 或使用 npm
npm create vitepress@latest

# 或使用 yarn
yarn create vitepress
```

### 安装依赖

```bash
cd your-project
pnpm install
```

## 项目结构

```
.
├── docs/
│   ├── .vitepress/
│   │   └── config.ts    # 配置文件
│   ├── guide/
│   │   ├── index.md
│   │   └── getting-started.md
│   └── index.md         # 首页
└── package.json
```

## 开发

启动开发服务器：

```bash
pnpm run dev
```

VitePress 将在 `http://localhost:5173` 启动开发服务器。

## 构建

构建生产版本：

```bash
pnpm run build
```

构建后的文件将输出到 `docs/.vitepress/dist` 目录。

## 预览

预览构建后的站点：

```bash
pnpm run serve
```

## 基本配置

编辑 `docs/.vitepress/config.ts` 文件来配置你的站点：

```typescript
import { defineConfig } from 'vitepress'

export default defineConfig({
  title: '我的站点',
  description: '站点描述',
  
  themeConfig: {
    nav: [
      { text: '首页', link: '/' },
      { text: '指南', link: '/guide/' }
    ],
    
    sidebar: [
      {
        text: '指南',
        items: [
          { text: '介绍', link: '/guide/' },
          { text: '快速开始', link: '/guide/getting-started' }
        ]
      }
    ]
  }
})
```

## 编写内容

VitePress 使用 Markdown 编写内容，支持：

- 标准 Markdown 语法
- Vue 组件
- 自定义容器
- 代码高亮
- 数学公式

### 示例

```markdown
# 标题

这是一个段落。

::: tip 提示
这是一个提示框。
:::

\`\`\`javascript
console.log('Hello VitePress!')
\`\`\`
```

## 下一步

- 了解更多 [配置选项](/config/)
- 学习 [Markdown 扩展](https://vitepress.vuejs.org/guide/markdown)
- 探索 [主题定制](https://vitepress.vuejs.org/guide/theme-introduction)
