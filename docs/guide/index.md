# 介绍

欢迎使用 VitePress！这是一个基于 Vite 和 Vue 的静态站点生成器。

## 什么是 VitePress？

VitePress 是 VuePress 的精神继承者，专为构建快速、内容为中心的网站而设计。

### 主要特性

- **快速开发**: 基于 Vite 的极速热重载
- **Vue 驱动**: 使用 Vue 3 和 Composition API
- **Markdown 增强**: 支持 Vue 组件和高级 Markdown 功能
- **主题系统**: 灵活的主题定制能力
- **SEO 友好**: 自动生成 sitemap 和 meta 标签

## 为什么选择 VitePress？

1. **性能优异**: 基于 Vite 的构建系统提供极快的开发体验
2. **简单易用**: 最小化配置，专注于内容创作
3. **现代化**: 使用最新的 Web 技术栈
4. **可扩展**: 支持自定义主题和插件

::: tip 提示
VitePress 特别适合构建文档站点、博客和营销页面。
:::

::: warning 注意
VitePress 目前还在 alpha 阶段，API 可能会有变化。
:::

## 下一步

- [快速开始](./getting-started.md) - 学习如何创建你的第一个 VitePress 站点
- [配置指南](/config/) - 了解如何配置你的站点
