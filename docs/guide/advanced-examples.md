# 高级示例

这里展示一些更复杂的交互式示例，包括第三方库集成和高级功能。

## 图表可视化

### 简单图表组件

<script setup>
import { ref, computed, onMounted } from 'vue'

const chartData = ref([
  { label: 'Vue.js', value: 85, color: '#4fc08d' },
  { label: 'React', value: 78, color: '#61dafb' },
  { label: 'Angular', value: 65, color: '#dd0031' },
  { label: 'Svelte', value: 72, color: '#ff3e00' }
])

const total = computed(() => chartData.value.reduce((sum, item) => sum + item.value, 0))

const pieSlices = computed(() => {
  let currentAngle = 0
  return chartData.value.map(item => {
    const percentage = item.value / total.value
    const angle = percentage * 360
    const slice = {
      ...item,
      percentage: Math.round(percentage * 100),
      startAngle: currentAngle,
      endAngle: currentAngle + angle
    }
    currentAngle += angle
    return slice
  })
})

const createPieSlice = (slice, radius = 80) => {
  const { startAngle, endAngle, color } = slice
  const start = (startAngle - 90) * Math.PI / 180
  const end = (endAngle - 90) * Math.PI / 180
  
  const x1 = 100 + radius * Math.cos(start)
  const y1 = 100 + radius * Math.sin(start)
  const x2 = 100 + radius * Math.cos(end)
  const y2 = 100 + radius * Math.sin(end)
  
  const largeArc = endAngle - startAngle > 180 ? 1 : 0
  
  return `M 100 100 L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArc} 1 ${x2} ${y2} Z`
}
</script>

<div class="demo-container">
  <div class="chart-container">
    <h4>前端框架受欢迎程度</h4>
    
    <div class="chart-wrapper">
      <svg width="200" height="200" class="pie-chart">
        <path
          v-for="slice in pieSlices"
          :key="slice.label"
          :d="createPieSlice(slice)"
          :fill="slice.color"
          :stroke="'white'"
          :stroke-width="2"
          class="pie-slice"
        />
      </svg>
      
      <div class="chart-legend">
        <div 
          v-for="slice in pieSlices" 
          :key="slice.label"
          class="legend-item"
        >
          <div 
            class="legend-color" 
            :style="{ backgroundColor: slice.color }"
          ></div>
          <span>{{ slice.label }} ({{ slice.percentage }}%)</span>
        </div>
      </div>
    </div>
  </div>
</div>

## 实时数据模拟

### 股票价格模拟器

<script setup>
const stocks = ref([
  { symbol: 'AAPL', name: 'Apple Inc.', price: 150.00, change: 0 },
  { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 2800.00, change: 0 },
  { symbol: 'MSFT', name: 'Microsoft Corp.', price: 300.00, change: 0 },
  { symbol: 'TSLA', name: 'Tesla Inc.', price: 800.00, change: 0 }
])

const isRunning = ref(false)
let intervalId = null

const startSimulation = () => {
  if (isRunning.value) return
  
  isRunning.value = true
  intervalId = setInterval(() => {
    stocks.value.forEach(stock => {
      const changePercent = (Math.random() - 0.5) * 0.1 // ±5%
      const oldPrice = stock.price
      stock.price = Math.max(0.01, stock.price * (1 + changePercent))
      stock.change = ((stock.price - oldPrice) / oldPrice) * 100
    })
  }, 1000)
}

const stopSimulation = () => {
  isRunning.value = false
  if (intervalId) {
    clearInterval(intervalId)
    intervalId = null
  }
}

const resetPrices = () => {
  stopSimulation()
  stocks.value = [
    { symbol: 'AAPL', name: 'Apple Inc.', price: 150.00, change: 0 },
    { symbol: 'GOOGL', name: 'Alphabet Inc.', price: 2800.00, change: 0 },
    { symbol: 'MSFT', name: 'Microsoft Corp.', price: 300.00, change: 0 },
    { symbol: 'TSLA', name: 'Tesla Inc.', price: 800.00, change: 0 }
  ]
}

onUnmounted(() => {
  stopSimulation()
})
</script>

<div class="demo-container">
  <div class="stock-simulator">
    <h4>股票价格模拟器</h4>
    
    <div class="controls">
      <button 
        @click="startSimulation" 
        :disabled="isRunning"
        class="btn btn-primary"
      >
        开始模拟
      </button>
      <button 
        @click="stopSimulation" 
        :disabled="!isRunning"
        class="btn btn-secondary"
      >
        停止模拟
      </button>
      <button 
        @click="resetPrices"
        class="btn btn-outline"
      >
        重置价格
      </button>
    </div>
    
    <div class="stock-list">
      <div 
        v-for="stock in stocks" 
        :key="stock.symbol"
        class="stock-item"
        :class="{
          'positive': stock.change > 0,
          'negative': stock.change < 0
        }"
      >
        <div class="stock-info">
          <div class="stock-symbol">{{ stock.symbol }}</div>
          <div class="stock-name">{{ stock.name }}</div>
        </div>
        <div class="stock-price">
          <div class="price">${{ stock.price.toFixed(2) }}</div>
          <div class="change" v-if="stock.change !== 0">
            {{ stock.change > 0 ? '+' : '' }}{{ stock.change.toFixed(2) }}%
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

## 游戏示例

### 简单的贪吃蛇游戏

<script setup>
const gameBoard = ref(null)
const gameSize = 20
const snake = ref([{ x: 10, y: 10 }])
const food = ref({ x: 15, y: 15 })
const direction = ref({ x: 0, y: 0 })
const gameRunning = ref(false)
const score = ref(0)
let gameLoop = null

const startGame = () => {
  if (gameRunning.value) return
  
  snake.value = [{ x: 10, y: 10 }]
  food.value = generateFood()
  direction.value = { x: 1, y: 0 }
  gameRunning.value = true
  score.value = 0
  
  gameLoop = setInterval(moveSnake, 200)
}

const stopGame = () => {
  gameRunning.value = false
  if (gameLoop) {
    clearInterval(gameLoop)
    gameLoop = null
  }
}

const generateFood = () => {
  let newFood
  do {
    newFood = {
      x: Math.floor(Math.random() * gameSize),
      y: Math.floor(Math.random() * gameSize)
    }
  } while (snake.value.some(segment => segment.x === newFood.x && segment.y === newFood.y))
  
  return newFood
}

const moveSnake = () => {
  const head = { ...snake.value[0] }
  head.x += direction.value.x
  head.y += direction.value.y
  
  // 检查边界碰撞
  if (head.x < 0 || head.x >= gameSize || head.y < 0 || head.y >= gameSize) {
    stopGame()
    alert(`游戏结束！得分: ${score.value}`)
    return
  }
  
  // 检查自身碰撞
  if (snake.value.some(segment => segment.x === head.x && segment.y === head.y)) {
    stopGame()
    alert(`游戏结束！得分: ${score.value}`)
    return
  }
  
  snake.value.unshift(head)
  
  // 检查是否吃到食物
  if (head.x === food.value.x && head.y === food.value.y) {
    score.value += 10
    food.value = generateFood()
  } else {
    snake.value.pop()
  }
}

const handleKeyPress = (event) => {
  if (!gameRunning.value) return
  
  switch (event.key) {
    case 'ArrowUp':
      if (direction.value.y === 0) direction.value = { x: 0, y: -1 }
      break
    case 'ArrowDown':
      if (direction.value.y === 0) direction.value = { x: 0, y: 1 }
      break
    case 'ArrowLeft':
      if (direction.value.x === 0) direction.value = { x: -1, y: 0 }
      break
    case 'ArrowRight':
      if (direction.value.x === 0) direction.value = { x: 1, y: 0 }
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  stopGame()
  document.removeEventListener('keydown', handleKeyPress)
})
</script>

<div class="demo-container">
  <div class="snake-game">
    <h4>贪吃蛇游戏</h4>
    
    <div class="game-info">
      <div class="score">得分: {{ score }}</div>
      <div class="controls">
        <button @click="startGame" :disabled="gameRunning" class="btn btn-primary">
          开始游戏
        </button>
        <button @click="stopGame" :disabled="!gameRunning" class="btn btn-secondary">
          停止游戏
        </button>
      </div>
    </div>
    
    <div class="game-board" ref="gameBoard">
      <div 
        v-for="y in gameSize" 
        :key="y"
        class="game-row"
      >
        <div 
          v-for="x in gameSize" 
          :key="x"
          class="game-cell"
          :class="{
            'snake': snake.some(segment => segment.x === x-1 && segment.y === y-1),
            'food': food.x === x-1 && food.y === y-1,
            'head': snake[0] && snake[0].x === x-1 && snake[0].y === y-1
          }"
        ></div>
      </div>
    </div>
    
    <div class="game-instructions">
      <p>使用方向键控制蛇的移动</p>
      <p>吃到红色食物得分，避免撞墙和撞到自己</p>
    </div>
  </div>
</div>

<style scoped>
.demo-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  background: var(--vp-c-bg-alt);
}

/* 图表样式 */
.chart-container h4 {
  text-align: center;
  margin-bottom: 20px;
}

.chart-wrapper {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: center;
}

.pie-chart {
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
}

.pie-slice {
  transition: all 0.3s ease;
  cursor: pointer;
}

.pie-slice:hover {
  filter: brightness(1.1);
  transform-origin: center;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

/* 股票模拟器样式 */
.stock-simulator h4 {
  text-align: center;
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--vp-c-brand-1);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--vp-c-brand-2);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-outline {
  background: transparent;
  border: 1px solid var(--vp-c-border);
  color: var(--vp-c-text-1);
}

.stock-list {
  display: grid;
  gap: 10px;
}

.stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: var(--vp-c-bg);
  border-radius: 8px;
  border-left: 4px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stock-item.positive {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.stock-item.negative {
  border-left-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.stock-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stock-symbol {
  font-weight: bold;
  font-size: 16px;
}

.stock-name {
  font-size: 12px;
  color: var(--vp-c-text-2);
}

.stock-price {
  text-align: right;
}

.price {
  font-weight: bold;
  font-size: 16px;
}

.change {
  font-size: 12px;
  font-weight: 500;
}

.positive .change {
  color: #10b981;
}

.negative .change {
  color: #ef4444;
}

/* 贪吃蛇游戏样式 */
.snake-game h4 {
  text-align: center;
  margin-bottom: 20px;
}

.game-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.score {
  font-weight: bold;
  font-size: 18px;
}

.game-board {
  display: inline-block;
  border: 2px solid var(--vp-c-border);
  border-radius: 4px;
  background: #f8f9fa;
  margin: 0 auto;
  display: block;
  width: fit-content;
}

.game-row {
  display: flex;
}

.game-cell {
  width: 15px;
  height: 15px;
  border: 1px solid rgba(0,0,0,0.1);
  background: white;
}

.game-cell.snake {
  background: #10b981;
}

.game-cell.head {
  background: #059669;
}

.game-cell.food {
  background: #ef4444;
  border-radius: 50%;
}

.game-instructions {
  margin-top: 15px;
  text-align: center;
  font-size: 14px;
  color: var(--vp-c-text-2);
}

.game-instructions p {
  margin: 5px 0;
}
</style>
