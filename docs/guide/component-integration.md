# 组件集成

本页面展示如何在VitePress中集成和使用自定义组件，以及如何创建可复用的交互式组件。

## 自定义组件示例

### 代码高亮器

<script setup>
import { ref, computed } from 'vue'

const code = ref(`function fibon<PERSON>ci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + <PERSON><PERSON><PERSON><PERSON>(n - 2);
}

console.log(fibonacci(10)); // 55`)

const language = ref('javascript')
const theme = ref('dark')

const languages = [
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'python', label: 'Python' },
  { value: 'css', label: 'CSS' }
]

const themes = [
  { value: 'dark', label: '深色主题' },
  { value: 'light', label: '浅色主题' }
]

const highlightedCode = computed(() => {
  // 简单的语法高亮模拟
  let highlighted = code.value
  
  // JavaScript/TypeScript 关键字
  if (language.value === 'javascript' || language.value === 'typescript') {
    highlighted = highlighted
      .replace(/\b(function|const|let|var|if|else|return|for|while)\b/g, '<span class="keyword">$1</span>')
      .replace(/\b(console|Math)\b/g, '<span class="builtin">$1</span>')
      .replace(/\/\/.*$/gm, '<span class="comment">$&</span>')
      .replace(/\d+/g, '<span class="number">$&</span>')
      .replace(/"[^"]*"/g, '<span class="string">$&</span>')
  }
  
  return highlighted
})
</script>

<div class="demo-container">
  <div class="code-highlighter">
    <h4>代码高亮器</h4>
    
    <div class="controls">
      <div class="control-group">
        <label>语言:</label>
        <select v-model="language">
          <option v-for="lang in languages" :key="lang.value" :value="lang.value">
            {{ lang.label }}
          </option>
        </select>
      </div>
      
      <div class="control-group">
        <label>主题:</label>
        <select v-model="theme">
          <option v-for="t in themes" :key="t.value" :value="t.value">
            {{ t.label }}
          </option>
        </select>
      </div>
    </div>
    
    <div class="editor-container">
      <textarea 
        v-model="code" 
        class="code-editor"
        placeholder="输入代码..."
      ></textarea>
      
      <div 
        class="code-preview" 
        :class="theme"
        v-html="highlightedCode"
      ></div>
    </div>
  </div>
</div>

### 响应式卡片组件

<script setup>
const cards = ref([
  {
    id: 1,
    title: 'Vue.js',
    description: '渐进式JavaScript框架',
    image: 'https://vuejs.org/logo.svg',
    tags: ['前端', '框架', 'JavaScript'],
    likes: 1250,
    liked: false
  },
  {
    id: 2,
    title: 'VitePress',
    description: '基于Vite的静态站点生成器',
    image: 'https://vitepress.vuejs.org/vitepress-logo-mini.svg',
    tags: ['文档', '静态站点', 'Vue'],
    likes: 890,
    liked: false
  },
  {
    id: 3,
    title: 'TypeScript',
    description: 'JavaScript的超集',
    image: 'https://www.typescriptlang.org/favicon-32x32.png',
    tags: ['语言', '类型安全', 'JavaScript'],
    likes: 2100,
    liked: true
  }
])

const toggleLike = (card) => {
  card.liked = !card.liked
  card.likes += card.liked ? 1 : -1
}

const filterTag = ref('')
const filteredCards = computed(() => {
  if (!filterTag.value) return cards.value
  return cards.value.filter(card => 
    card.tags.some(tag => tag.toLowerCase().includes(filterTag.value.toLowerCase()))
  )
})

const allTags = computed(() => {
  const tags = new Set()
  cards.value.forEach(card => {
    card.tags.forEach(tag => tags.add(tag))
  })
  return Array.from(tags)
})
</script>

<div class="demo-container">
  <div class="card-gallery">
    <h4>响应式卡片组件</h4>
    
    <div class="filter-section">
      <input 
        v-model="filterTag" 
        type="text" 
        placeholder="按标签筛选..."
        class="filter-input"
      />
      
      <div class="tag-filters">
        <button 
          @click="filterTag = ''"
          :class="{ active: !filterTag }"
          class="tag-filter"
        >
          全部
        </button>
        <button 
          v-for="tag in allTags"
          :key="tag"
          @click="filterTag = tag"
          :class="{ active: filterTag === tag }"
          class="tag-filter"
        >
          {{ tag }}
        </button>
      </div>
    </div>
    
    <div class="cards-grid">
      <div 
        v-for="card in filteredCards" 
        :key="card.id"
        class="card"
      >
        <div class="card-image">
          <img :src="card.image" :alt="card.title" />
        </div>
        
        <div class="card-content">
          <h5 class="card-title">{{ card.title }}</h5>
          <p class="card-description">{{ card.description }}</p>
          
          <div class="card-tags">
            <span 
              v-for="tag in card.tags" 
              :key="tag"
              class="tag"
            >
              {{ tag }}
            </span>
          </div>
          
          <div class="card-actions">
            <button 
              @click="toggleLike(card)"
              :class="{ liked: card.liked }"
              class="like-btn"
            >
              {{ card.liked ? '❤️' : '🤍' }} {{ card.likes }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

### 数据表格组件

<script setup>
const tableData = ref([
  { id: 1, name: '张三', age: 25, city: '北京', salary: 8000, department: '技术部' },
  { id: 2, name: '李四', age: 30, city: '上海', salary: 12000, department: '产品部' },
  { id: 3, name: '王五', age: 28, city: '深圳', salary: 10000, department: '设计部' },
  { id: 4, name: '赵六', age: 32, city: '杭州', salary: 15000, department: '技术部' },
  { id: 5, name: '钱七', age: 26, city: '广州', salary: 9000, department: '市场部' }
])

const sortKey = ref('')
const sortOrder = ref('asc')
const searchQuery = ref('')

const sortedData = computed(() => {
  let data = [...tableData.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    data = data.filter(item => 
      Object.values(item).some(value => 
        String(value).toLowerCase().includes(searchQuery.value.toLowerCase())
      )
    )
  }
  
  // 排序
  if (sortKey.value) {
    data.sort((a, b) => {
      let aVal = a[sortKey.value]
      let bVal = b[sortKey.value]
      
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return sortOrder.value === 'asc' ? aVal - bVal : bVal - aVal
      } else {
        aVal = String(aVal).toLowerCase()
        bVal = String(bVal).toLowerCase()
        if (sortOrder.value === 'asc') {
          return aVal < bVal ? -1 : aVal > bVal ? 1 : 0
        } else {
          return aVal > bVal ? -1 : aVal < bVal ? 1 : 0
        }
      }
    })
  }
  
  return data
})

const sort = (key) => {
  if (sortKey.value === key) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortKey.value = key
    sortOrder.value = 'asc'
  }
}

const getSortIcon = (key) => {
  if (sortKey.value !== key) return '↕️'
  return sortOrder.value === 'asc' ? '↑' : '↓'
}
</script>

<div class="demo-container">
  <div class="data-table">
    <h4>数据表格组件</h4>
    
    <div class="table-controls">
      <input 
        v-model="searchQuery"
        type="text"
        placeholder="搜索..."
        class="search-input"
      />
    </div>
    
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th @click="sort('name')" class="sortable">
              姓名 {{ getSortIcon('name') }}
            </th>
            <th @click="sort('age')" class="sortable">
              年龄 {{ getSortIcon('age') }}
            </th>
            <th @click="sort('city')" class="sortable">
              城市 {{ getSortIcon('city') }}
            </th>
            <th @click="sort('salary')" class="sortable">
              薪资 {{ getSortIcon('salary') }}
            </th>
            <th @click="sort('department')" class="sortable">
              部门 {{ getSortIcon('department') }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in sortedData" :key="item.id">
            <td>{{ item.name }}</td>
            <td>{{ item.age }}</td>
            <td>{{ item.city }}</td>
            <td>¥{{ item.salary.toLocaleString() }}</td>
            <td>{{ item.department }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    
    <div class="table-info">
      显示 {{ sortedData.length }} / {{ tableData.length }} 条记录
    </div>
  </div>
</div>

<style scoped>
.demo-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  background: var(--vp-c-bg-alt);
}

/* 代码高亮器样式 */
.code-highlighter h4 {
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  font-weight: 500;
  min-width: 40px;
}

.control-group select {
  padding: 4px 8px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  background: var(--vp-c-bg);
}

.editor-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  height: 200px;
}

.code-editor {
  width: 100%;
  height: 100%;
  padding: 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  resize: none;
  background: var(--vp-c-bg);
}

.code-preview {
  padding: 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  overflow: auto;
  white-space: pre-wrap;
}

.code-preview.dark {
  background: #1e1e1e;
  color: #d4d4d4;
}

.code-preview.light {
  background: #f8f8f8;
  color: #333;
}

.code-preview .keyword {
  color: #569cd6;
  font-weight: bold;
}

.code-preview .builtin {
  color: #4ec9b0;
}

.code-preview .comment {
  color: #6a9955;
  font-style: italic;
}

.code-preview .number {
  color: #b5cea8;
}

.code-preview .string {
  color: #ce9178;
}

/* 卡片组件样式 */
.card-gallery h4 {
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  margin-bottom: 10px;
  background: var(--vp-c-bg);
}

.tag-filters {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag-filter {
  padding: 4px 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 20px;
  background: var(--vp-c-bg);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.tag-filter:hover,
.tag-filter.active {
  background: var(--vp-c-brand-1);
  color: white;
  border-color: var(--vp-c-brand-1);
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.card {
  background: var(--vp-c-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.card-image {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.card-image img {
  max-width: 60px;
  max-height: 60px;
}

.card-content {
  padding: 15px;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: bold;
}

.card-description {
  margin: 0 0 12px 0;
  color: var(--vp-c-text-2);
  font-size: 14px;
}

.card-tags {
  display: flex;
  gap: 6px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.tag {
  padding: 2px 8px;
  background: var(--vp-c-brand-1);
  color: white;
  border-radius: 12px;
  font-size: 11px;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.like-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.like-btn:hover {
  transform: scale(1.1);
}

/* 数据表格样式 */
.data-table h4 {
  margin-bottom: 20px;
}

.table-controls {
  margin-bottom: 15px;
}

.search-input {
  width: 100%;
  max-width: 300px;
  padding: 8px 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  background: var(--vp-c-bg);
}

.table-container {
  overflow-x: auto;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--vp-c-bg);
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--vp-c-border);
}

.table th {
  background: var(--vp-c-bg-alt);
  font-weight: bold;
}

.table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background 0.2s;
}

.table th.sortable:hover {
  background: var(--vp-c-border);
}

.table tbody tr:hover {
  background: var(--vp-c-bg-alt);
}

.table-info {
  margin-top: 10px;
  font-size: 14px;
  color: var(--vp-c-text-2);
}

@media (max-width: 768px) {
  .editor-container {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .code-editor,
  .code-preview {
    height: 150px;
  }
  
  .controls {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
