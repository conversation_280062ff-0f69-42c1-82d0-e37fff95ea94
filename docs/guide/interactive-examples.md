# 交互式示例

VitePress 支持在 Markdown 中嵌入可运行的示例代码，让文档更加生动和实用。

## Vue 组件示例

### 基础计数器

<script setup>
import { ref } from 'vue'

const count = ref(0)
const increment = () => count.value++
const decrement = () => count.value--
</script>

<div class="demo-container">
  <div class="counter">
    <button @click="decrement" class="btn">-</button>
    <span class="count">{{ count }}</span>
    <button @click="increment" class="btn">+</button>
  </div>
  <p>当前计数: {{ count }}</p>
</div>

```vue
<script setup>
import { ref } from 'vue'

const count = ref(0)
const increment = () => count.value++
const decrement = () => count.value--
</script>

<template>
  <div class="counter">
    <button @click="decrement">-</button>
    <span>{{ count }}</span>
    <button @click="increment">+</button>
  </div>
</template>
```

### 响应式表单

<script setup>
const form = ref({
  name: '',
  email: '',
  message: ''
})

const submitForm = () => {
  alert(`提交成功！\n姓名: ${form.value.name}\n邮箱: ${form.value.email}`)
}
</script>

<div class="demo-container">
  <form @submit.prevent="submitForm" class="form">
    <div class="form-group">
      <label>姓名:</label>
      <input v-model="form.name" type="text" placeholder="请输入姓名" />
    </div>
    <div class="form-group">
      <label>邮箱:</label>
      <input v-model="form.email" type="email" placeholder="请输入邮箱" />
    </div>
    <div class="form-group">
      <label>留言:</label>
      <textarea v-model="form.message" placeholder="请输入留言"></textarea>
    </div>
    <button type="submit" class="submit-btn">提交</button>
  </form>
  
  <div class="preview">
    <h4>实时预览:</h4>
    <p><strong>姓名:</strong> {{ form.name || '未填写' }}</p>
    <p><strong>邮箱:</strong> {{ form.email || '未填写' }}</p>
    <p><strong>留言:</strong> {{ form.message || '未填写' }}</p>
  </div>
</div>

```vue
<script setup>
import { ref } from 'vue'

const form = ref({
  name: '',
  email: '',
  message: ''
})

const submitForm = () => {
  alert(`提交成功！\n姓名: ${form.value.name}\n邮箱: ${form.value.email}`)
}
</script>

<template>
  <form @submit.prevent="submitForm">
    <input v-model="form.name" type="text" placeholder="姓名" />
    <input v-model="form.email" type="email" placeholder="邮箱" />
    <textarea v-model="form.message" placeholder="留言"></textarea>
    <button type="submit">提交</button>
  </form>
</template>
```

## CSS 动画示例

### 悬停效果

<div class="demo-container">
  <div class="hover-cards">
    <div class="card">悬停我</div>
    <div class="card card-rotate">旋转效果</div>
    <div class="card card-scale">缩放效果</div>
  </div>
</div>

```css
.card {
  padding: 20px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.card-rotate:hover {
  transform: rotate(5deg) scale(1.05);
}

.card-scale:hover {
  transform: scale(1.1);
}
```

## JavaScript 功能示例

### 主题切换

<script setup>
const isDark = ref(false)
const toggleTheme = () => {
  isDark.value = !isDark.value
}
</script>

<div class="demo-container">
  <div :class="['theme-demo', { dark: isDark }]">
    <h3>{{ isDark ? '深色主题' : '浅色主题' }}</h3>
    <p>这是一个主题切换示例</p>
    <button @click="toggleTheme" class="theme-btn">
      切换到{{ isDark ? '浅色' : '深色' }}主题
    </button>
  </div>
</div>

```javascript
const isDark = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  // 实际应用中可能需要更新 document.documentElement.classList
}
```

### 数据可视化

<script setup>
const data = ref([
  { name: 'Vue', value: 85 },
  { name: 'React', value: 78 },
  { name: 'Angular', value: 65 },
  { name: 'Svelte', value: 72 }
])

const maxValue = computed(() => Math.max(...data.value.map(item => item.value)))
</script>

<div class="demo-container">
  <div class="chart">
    <h4>框架受欢迎程度</h4>
    <div class="bars">
      <div v-for="item in data" :key="item.name" class="bar-item">
        <div class="bar-label">{{ item.name }}</div>
        <div class="bar-container">
          <div 
            class="bar" 
            :style="{ width: (item.value / maxValue * 100) + '%' }"
          ></div>
        </div>
        <div class="bar-value">{{ item.value }}%</div>
      </div>
    </div>
  </div>
</div>

## 实用工具示例

### 颜色选择器

<script setup>
const selectedColor = ref('#3498db')
const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c']
</script>

<div class="demo-container">
  <div class="color-picker">
    <h4>选择颜色</h4>
    <div class="color-palette">
      <div 
        v-for="color in colors" 
        :key="color"
        class="color-item"
        :class="{ active: selectedColor === color }"
        :style="{ backgroundColor: color }"
        @click="selectedColor = color"
      ></div>
    </div>
    <div class="color-preview" :style="{ backgroundColor: selectedColor }">
      <span>{{ selectedColor }}</span>
    </div>
  </div>
</div>

### 文本处理工具

<script setup>
const inputText = ref('Hello VitePress!')
const processedText = computed(() => ({
  uppercase: inputText.value.toUpperCase(),
  lowercase: inputText.value.toLowerCase(),
  reversed: inputText.value.split('').reverse().join(''),
  wordCount: inputText.value.trim().split(/\s+/).length
}))
</script>

<div class="demo-container">
  <div class="text-processor">
    <h4>文本处理工具</h4>
    <textarea 
      v-model="inputText" 
      placeholder="输入文本..."
      class="text-input"
    ></textarea>
    
    <div class="results">
      <div class="result-item">
        <strong>大写:</strong> {{ processedText.uppercase }}
      </div>
      <div class="result-item">
        <strong>小写:</strong> {{ processedText.lowercase }}
      </div>
      <div class="result-item">
        <strong>反转:</strong> {{ processedText.reversed }}
      </div>
      <div class="result-item">
        <strong>字数:</strong> {{ processedText.wordCount }}
      </div>
    </div>
  </div>
</div>

<style scoped>
.demo-container {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  background: var(--vp-c-bg-alt);
}

/* 计数器样式 */
.counter {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: center;
  margin: 20px 0;
}

.btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--vp-c-brand-1);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:hover {
  background: var(--vp-c-brand-2);
  transform: scale(1.1);
}

.count {
  font-size: 24px;
  font-weight: bold;
  min-width: 40px;
  text-align: center;
}

/* 表单样式 */
.form {
  max-width: 400px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  height: 80px;
  resize: vertical;
}

.submit-btn {
  background: var(--vp-c-brand-1);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.preview {
  margin-top: 20px;
  padding: 15px;
  background: var(--vp-c-bg);
  border-radius: 4px;
}

/* 卡片样式 */
.hover-cards {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.card {
  padding: 20px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-width: 120px;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.card-rotate:hover {
  transform: rotate(5deg) scale(1.05);
}

.card-scale:hover {
  transform: scale(1.1);
}

/* 主题切换样式 */
.theme-demo {
  padding: 20px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: #f8f9fa;
  color: #333;
}

.theme-demo.dark {
  background: #2d3748;
  color: #e2e8f0;
}

.theme-btn {
  background: var(--vp-c-brand-1);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

/* 图表样式 */
.chart {
  max-width: 400px;
}

.bars {
  margin-top: 15px;
}

.bar-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.bar-label {
  width: 60px;
  font-size: 14px;
}

.bar-container {
  flex: 1;
  height: 20px;
  background: #e2e8f0;
  border-radius: 10px;
  overflow: hidden;
}

.bar {
  height: 100%;
  background: linear-gradient(90deg, var(--vp-c-brand-1), var(--vp-c-brand-2));
  transition: width 0.5s ease;
}

.bar-value {
  width: 40px;
  text-align: right;
  font-size: 12px;
}

/* 颜色选择器样式 */
.color-palette {
  display: flex;
  gap: 10px;
  margin: 15px 0;
}

.color-item {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.2s;
}

.color-item:hover,
.color-item.active {
  border-color: var(--vp-c-text-1);
  transform: scale(1.1);
}

.color-preview {
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* 文本处理工具样式 */
.text-input {
  width: 100%;
  height: 80px;
  padding: 10px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  resize: vertical;
  margin-bottom: 15px;
}

.results {
  display: grid;
  gap: 10px;
}

.result-item {
  padding: 10px;
  background: var(--vp-c-bg);
  border-radius: 4px;
  border-left: 4px solid var(--vp-c-brand-1);
}
</style>
